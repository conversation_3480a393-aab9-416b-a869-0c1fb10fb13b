import React, { useState } from "react";
import "./index.css";

export default function InstructionBoard() {
	const [instructions, setInstructions] = useState([]);
	const [input, setInput] = useState("");
	const [error, setError] = useState(false);

	const addInstruction = (e) => {
		e.preventDefault();
		if (!input.trim()) {
			setError(true);
			setTimeout(() => setError(false), 3000);
			return;
		}
		setInstructions([...instructions, input.trim()]);
		setInput("");
	};

	const moveUp = (index) => {
		if (index === 0) return;
		const updated = [...instructions];
		[updated[index - 1], updated[index]] = [updated[index], updated[index - 1]];
		setInstructions(updated);
	};

	const moveDown = (index) => {
		if (index === instructions.length - 1) return;
		const updated = [...instructions];
		[updated[index + 1], updated[index]] = [updated[index], updated[index + 1]];
		setInstructions(updated);
	};

	return (
		<div className="mt-50 layout-column justify-content-center align-items-center">
			<form onSubmit={addInstruction}>
				<section className="my-30 layout-row align-items-center justify-content-center width-1000">
					<input
						id="instruction-input"
						type="text"
						placeholder="New Instruction"
						data-testid="instruction-input"
						value={input}
						onChange={(e) => setInput(e.target.value)}
						className="width-80"
					/>
					<button
						type="submit"
						className="ml-30 width-20"
						data-testid="add-instruction-button"
					>
						Add Instruction
					</button>
				</section>
				<span
					data-testid="error-message"
					className={`error ${error ? "" : "hidden"}`}
				>
					Please enter an instruction.
				</span>
			</form>

			<div className="card outlined mt-0 width-800">
				<div className="card-text">
					<h4>Instructions</h4>
					<ul
						className="styled mt-50"
						data-testid="instructions"
					>
						{instructions.map((inst, index) => (
							<li key={index}>
								<div className="li-content layout-row justify-content-between align-items-center">
									<span>{index + 1}</span>
									<span>{inst}</span>
									<div className="icons">
										<button
											className="icon-only x-medium mx-2"
											onClick={() => moveDown(index)}
											disabled={index === instructions.length - 1}
										>
											<i className="material-icons">arrow_drop_down</i>
										</button>
										<button
											className="icon-only x-medium mx-2"
											onClick={() => moveUp(index)}
											disabled={index === 0}
										>
											<i className="material-icons">arrow_drop_up</i>
										</button>
									</div>
								</div>
							</li>
						))}
					</ul>
				</div>
			</div>
		</div>
	);
}
